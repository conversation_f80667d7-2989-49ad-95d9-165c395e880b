package com.debate_ournament.config;

import java.nio.charset.StandardCharsets;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CharacterEncodingFilter;

import jakarta.annotation.PostConstruct;

/**
 * 字符编码配置类
 * 确保整个应用程序使用UTF-8编码
 *
 * <AUTHOR> Debate Platform
 * @version 1.0
 */
@Configuration
public class EncodingConfig {

    /**
     * 初始化系统字符编码
     */
    @PostConstruct
    public void initEncoding() {
        // 设置系统属性以确保UTF-8编码
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("sun.jnu.encoding", "UTF-8");
        System.setProperty("user.language", "zh");
        System.setProperty("user.country", "CN");
        System.setProperty("user.timezone", "Asia/Shanghai");

        // 输出编码信息到日志
        System.out.println("系统编码设置完成:");
        System.out.println("file.encoding: " + System.getProperty("file.encoding"));
        System.out.println("sun.jnu.encoding: " + System.getProperty("sun.jnu.encoding"));
        System.out.println("默认字符集: " + StandardCharsets.UTF_8.displayName());
    }

    /**
     * 字符编码过滤器
     * 确保HTTP请求和响应使用UTF-8编码
     */
    @Bean
    public FilterRegistrationBean<CharacterEncodingFilter> characterEncodingFilter() {
        FilterRegistrationBean<CharacterEncodingFilter> registration = new FilterRegistrationBean<>();
        CharacterEncodingFilter characterEncodingFilter = new CharacterEncodingFilter();

        // 设置编码为UTF-8
        characterEncodingFilter.setEncoding("UTF-8");
        // 强制使用UTF-8编码
        characterEncodingFilter.setForceEncoding(true);
        // 对请求和响应都强制使用UTF-8
        characterEncodingFilter.setForceRequestEncoding(true);
        characterEncodingFilter.setForceResponseEncoding(true);

        registration.setFilter(characterEncodingFilter);
        registration.addUrlPatterns("/*");
        registration.setName("CharacterEncodingFilter");
        registration.setOrder(1);

        return registration;
    }
}
