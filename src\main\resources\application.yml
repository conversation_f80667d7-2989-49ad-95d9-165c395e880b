# AI 辩论赛平台应用配置文件
# 环境: 开发环境
# 版本: 1.0

server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
      force-request: true
      force-response: true
  tomcat:
    uri-encoding: UTF-8

spring:
  application:
    name: ai-debate-tournament-platform

  # 允许循环依赖
  main:
    allow-circular-references: true

  # 数据源配置
  datasource:
    url: ***********************************************************************************************************************************************************
    username: root
    password: xiaoxiao123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接池配置
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 300000
      max-lifetime: 1200000
      connection-timeout: 20000
      pool-name: DatebookHikariCP
      connection-test-query: SELECT 1
      leak-detection-threshold: 60000
      auto-commit: true

  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: none # 不自动创建表，使用我们的初始化脚本
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        generate_statistics: false
    open-in-view: false

  # 数据库初始化配置 - 禁用Spring Boot默认的初始化
  sql:
    init:
      mode: never # 禁用默认的schema.sql和data.sql

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 2KB

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000

  # 邮件配置（开发环境）
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-auth-code
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          ssl:
            trust: smtp.qq.com

  # 国际化配置
  messages:
    basename: messages
    encoding: UTF-8
    fallback-to-system-locale: false

  # Jackson JSON配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    default-property-inclusion: non_null

  # 开发工具配置
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true

# 自定义应用配置
app:
  # 数据库初始化配置
  database:
    init:
      enabled: true                                    # 是否启用数据库自动初始化
      force: false                                     # 是否强制重新初始化（谨慎使用）
      script-path: db/init-database-merged.sql         # 初始化脚本路径
      check-on-startup: true                           # 启动时检查数据库状态

  # JWT配置
  jwt:
    secret: YourSecretKeyHere_ChangeThisInProduction_AtLeast256Bits_ForSecurity
    expiration: 86400000 # 24小时（毫秒）
    refresh-expiration: 604800000 # 7天（毫秒）

  # 安全配置
  security:
    cors:
      allowed-origins:
        - http://localhost:3000
        - http://localhost:5173
        - http://localhost:5174
        - http://localhost:5175
        - http://localhost:5176
        - http://localhost:8080
        - http://127.0.0.1:3000
        - http://127.0.0.1:5173
        - http://127.0.0.1:5174
        - http://127.0.0.1:5175
        - http://127.0.0.1:5176
      allowed-methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
      allowed-headers: "*"
      allow-credentials: true
      max-age: 3600

    # 验证码配置
    captcha:
      enabled: true
      length: 4
      width: 120
      height: 40
      expire-time: 300 # 5分钟（秒）

    # 密码策略配置
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-numbers: true
      require-special-chars: false
      max-attempts: 5
      lockout-duration: 1800 # 30分钟（秒）

  # 文件存储配置
  file:
    storage:
      type: local # local, oss, s3
      base-path: uploads
      avatar:
        path: avatars
        max-size: 5MB
        allowed-types:
          - image/jpeg
          - image/png
          - image/gif
          - image/webp

  # 邮件配置（开发环境）
  mail:
    enabled: false # 开发环境下禁用邮件发送
    smtp:
      host: smtp.qq.com
      port: 587
      username: <EMAIL>
      password: your-auth-code
      from: <EMAIL>

  # 邮箱验证配置
  email:
    verification:
      expiration: 1440 # 24小时（分钟）
      resend-interval: 60 # 1分钟（秒）

  # 密码重置配置
  password:
    reset:
      expiration: 60 # 1小时（分钟）
      max-attempts: 3

  # 账号锁定配置
  account:
    lock:
      max-attempts: 5
      lock-duration: 30 # 30分钟

  # 文件上传配置
  upload:
    path: uploads
    avatar:
      max-size: 2097152 # 2MB
      allowed-types: jpg,jpeg,png,gif,webp

  # 文件清理配置
  cleanup:
    orphaned-files:
      enabled: true
      max-age-days: 7

  # 加密配置
  encryption:
    enabled: true
    key-store-path: keys
    public-key-file-name: public_key.pem
    private-key-file-name: private_key.pem
    key-id-file-name: key_id.txt
    rsa-key-size: 2048
    key-rotation-interval-hours: 24
    auto-generate-on-startup: true
    enable-key-rotation: false

  # AI模型配置
  ai:
    models:
      default: openai-gpt-3.5-turbo
      available:
        - name: openai-gpt-3.5-turbo
          display-name: GPT-3.5 Turbo
          provider: openai
          enabled: true
        - name: openai-gpt-4
          display-name: GPT-4
          provider: openai
          enabled: true
        - name: claude-3-sonnet
          display-name: Claude 3 Sonnet
          provider: anthropic
          enabled: true
        - name: claude-3-haiku
          display-name: Claude 3 Haiku
          provider: anthropic
          enabled: true

# 日志配置 - 开发环境
logging:
  level:
    root: WARN
    com.debate_ournament: INFO
    com.debate_ournament.users.service: INFO
    com.debate_ournament.security: WARN
    org.springframework: WARN
    org.springframework.boot: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.springframework.data: WARN
    org.hibernate: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type: WARN
    com.zaxxer.hikari: WARN
    org.apache.catalina: WARN
    org.apache.tomcat: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30
  charset:
    console: UTF-8
    file: UTF-8

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,database-init
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true
    build:
      enabled: true
  metrics:
    export:
      simple:
        enabled: true

# 自定义应用信息
info:
  app:
    name: AI辩论赛平台
    description: 基于人工智能的在线辩论比赛平台
    version: 1.0.0
    encoding: UTF-8
    java:
      version: 17
    spring-boot:
      version: 3.x
