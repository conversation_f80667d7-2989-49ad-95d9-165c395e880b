import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import request from '@/api/index';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '');

  // 安全地解析localStorage中的userInfo
  const getUserInfoFromStorage = () => {
    try {
      const stored = localStorage.getItem('userInfo');
      if (!stored || stored === 'undefined' || stored === 'null') {
        return {};
      }
      return JSON.parse(stored);
    } catch (error) {
      console.warn('解析用户信息失败，使用默认值:', error);
      localStorage.removeItem('userInfo'); // 清除无效数据
      return {};
    }
  };

  const userInfo = ref(getUserInfoFromStorage());
  const roles = ref([]);
  const permissions = ref([]);

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const user = computed(() => userInfo.value); // 添加user计算属性以保持兼容性
  const username = computed(() => userInfo.value.username || '');
  const avatar = computed(() => userInfo.value.avatar || '');
  const userId = computed(() => userInfo.value.id || '');
  const email = computed(() => userInfo.value.email || '');

  // 设置Token
  function setToken(newToken) {
    token.value = newToken;
    localStorage.setItem('token', newToken);
  }

  // 设置用户信息
  function setUserInfo(info) {
    userInfo.value = info || {};
    try {
      localStorage.setItem('userInfo', JSON.stringify(info || {}));
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  }

  // 设置用户角色
  function setRoles(userRoles) {
    roles.value = userRoles;
  }

  // 设置用户权限
  function setPermissions(userPermissions) {
    permissions.value = userPermissions;
  }

  // 登录
  async function login(loginData) {
    try {
      const response = await request.post('/auth/login', loginData);

      if (response.code === 200) {
        setToken(response.data.token);
        setUserInfo(response.data.userInfo);
        return response.data;
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  // 注册
  async function register(registerData) {
    try {
      const response = await request.post('/auth/register', registerData);

      if (response.code === 200) {
        return response.data;
      } else {
        throw new Error(response.message || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  }

  // 获取用户信息
  async function getUserInfo() {
    try {
      const response = await request.get('/user/info');

      if (response.code === 200) {
        setUserInfo(response.data);
        setRoles(response.data.roles || []);
        setPermissions(response.data.permissions || []);
        return response.data;
      } else {
        throw new Error(response.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  // 退出登录
  async function logout() {
    try {
      if (token.value) {
        await request.post('/auth/logout');
      }
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      // 清除本地存储的用户信息
      clearUserState();
    }
  }

  // 清除用户状态
  function clearUserState() {
    token.value = '';
    userInfo.value = {};
    roles.value = [];
    permissions.value = [];

    // 安全地清除localStorage
    try {
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('rememberMe');
      localStorage.removeItem('username');
    } catch (error) {
      console.error('清除本地存储失败:', error);
    }
  }

  // 检查权限
  function hasPermission(permission) {
    return permissions.value.includes(permission);
  }

  // 检查角色
  function hasRole(role) {
    return roles.value.includes(role);
  }

  // 更新用户信息
  async function updateUserInfo(userData) {
    try {
      const response = await request.put('/user/info', userData);

      if (response.code === 200) {
        // 合并更新后的用户信息
        setUserInfo({
          ...userInfo.value,
          ...response.data
        });
        return response.data;
      } else {
        throw new Error(response.message || '更新用户信息失败');
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  // 修改密码
  async function changePassword(passwordData) {
    try {
      const response = await request.post('/user/change-password', passwordData);

      if (response.code === 200) {
        return response.data;
      } else {
        throw new Error(response.message || '修改密码失败');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  }

  return {
    token,
    userInfo,
    user,
    roles,
    permissions,
    isLoggedIn,
    username,
    avatar,
    userId,
    email,
    setToken,
    setUserInfo,
    setRoles,
    setPermissions,
    login,
    register,
    getUserInfo,
    logout,
    clearUserState,
    hasPermission,
    hasRole,
    updateUserInfo,
    changePassword
  };
});
