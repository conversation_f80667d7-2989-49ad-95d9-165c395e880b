2025-05-26 22:57:14.745 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 27812 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 22:57:14.746 [main] DEBUG c.d.AiDebateTournamentPlatformApplication - Running with Spring Boot v3.5.0, Spring v6.2.7
2025-05-26 22:57:14.747 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 22:57:17.944 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 22:57:18.037 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3197 ms
2025-05-26 22:57:19.632 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-26 22:57:21.332 [main] DEBUG org.hibernate.SQL -
    alter table chat_messages
       modify column ai_provider enum ('ALIBABA','ALIBABA_BAILIAN','ANTHROPIC','BAIDU','DEEPSEEK','MOONSHOT','OPENAI','OPENROUTER','SILICONFLOW','TENCENT','ZHIPU')
2025-05-26 22:57:21.347 [main] DEBUG org.hibernate.SQL -
    alter table mindmap_responses
       modify column mindmap_type enum ('ARGUMENT','COMPARISON','CONCEPT','FLOWCHART','HIERARCHY','KNOWLEDGE','NETWORK','TIMELINE')
2025-05-26 22:57:21.365 [main] DEBUG org.hibernate.SQL -
    alter table mindmap_responses
       modify column status enum ('CANCELLED','FAILED','PENDING','SUCCESS')
2025-05-26 22:57:21.386 [main] DEBUG org.hibernate.SQL -
    alter table user_levels
       modify column win_rate decimal(5,2) DEFAULT 0.00 COMMENT '胜率（百分比）' not null
2025-05-26 22:57:24.640 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-05-26 22:57:25.424 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 22:57:25.427 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复公钥
2025-05-26 22:57:25.428 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复私钥
2025-05-26 22:57:25.428 [main] DEBUG c.d.service.KeyManagementService - 成功加载现有密钥对
2025-05-26 22:57:25.428 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 22:57:25.989 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 109 mappings in 'requestMappingHandlerMapping'
2025-05-26 22:57:26.056 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /uploads/**, /avatars/**, /static/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-05-26 22:57:26.243 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 22:57:26.308 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name customUserDetailsService
2025-05-26 22:57:26.444 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter$$SpringCGLIB$$0, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-05-26 22:57:26.751 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-26 22:57:26.812 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-26 22:57:27.380 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 22:57:27.404 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 13.499 seconds (process running for 14.048)
2025-05-26 22:57:27.494 [main] DEBUG c.d.config.ApplicationStartupConfig - 主上传目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads
2025-05-26 22:57:27.495 [main] DEBUG c.d.config.ApplicationStartupConfig - 头像上传目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\avatars
2025-05-26 22:57:27.495 [main] DEBUG c.d.config.ApplicationStartupConfig - 临时文件目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\temp
2025-05-26 22:57:27.495 [main] DEBUG c.d.config.ApplicationStartupConfig - 备份目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\backup
2025-05-26 22:57:27.495 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 22:57:27.495 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 22:57:27.519 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 22:57:27.521 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 5, 活跃用户数: 5
2025-05-26 22:58:22.055 [http-nio-8088-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-26 22:58:22.056 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-05-26 22:58:22.056 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-05-26 22:58:22.056 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-05-26 22:58:22.058 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@23344ec3
2025-05-26 22:58:22.059 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@5064fecc
2025-05-26 22:58:22.060 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-26 22:58:22.060 [http-nio-8088-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-05-26 22:58:22.088 [http-nio-8088-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /auth/captcha/generate
2025-05-26 22:58:22.105 [http-nio-8088-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-05-26 22:58:22.113 [http-nio-8088-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /auth/captcha/generate
2025-05-26 22:58:22.116 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/auth/captcha/generate", parameters={}
2025-05-26 22:58:22.118 [http-nio-8088-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.debate_ournament.users.controller.CaptchaController#generateCaptcha(HttpServletRequest, HttpSession)
2025-05-26 22:58:22.163 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, clientIp=127.0.0.1
2025-05-26 22:58:22.163 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 22:58:22.218 [http-nio-8088-exec-2] DEBUG org.hibernate.SQL -
    delete c1_0
    from
        captchas c1_0
    where
        c1_0.expires_at<?
2025-05-26 22:58:22.270 [http-nio-8088-exec-2] DEBUG org.hibernate.SQL -
    select
        count(c1_0.session_id)
    from
        captchas c1_0
    where
        c1_0.client_ip=?
        and c1_0.created_at>?
2025-05-26 22:58:22.301 [http-nio-8088-exec-2] DEBUG org.hibernate.SQL -
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used
    from
        captchas c1_0
    where
        c1_0.session_id=?
2025-05-26 22:58:22.504 [http-nio-8088-exec-2] DEBUG org.hibernate.SQL -
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used
    from
        captchas c1_0
    where
        c1_0.session_id=?
2025-05-26 22:58:22.521 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, code=TCRJ
2025-05-26 22:58:22.558 [http-nio-8088-exec-2] DEBUG org.hibernate.SQL -
    insert
    into
        captchas
        (attempt_count, client_ip, code, created_at, expires_at, used, session_id)
    values
        (?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:58:22.629 [http-nio-8088-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-05-26 22:58:22.631 [http-nio-8088-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ApiResponse{code=200, message='验证码生成成功', data={sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, message=验 (truncated)...]
2025-05-26 22:58:22.656 [http-nio-8088-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-05-26 22:59:38.146 [http-nio-8088-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/register
2025-05-26 22:59:38.148 [http-nio-8088-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-05-26 22:59:38.149 [http-nio-8088-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/register
2025-05-26 22:59:38.150 [http-nio-8088-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/register", parameters={}
2025-05-26 22:59:38.150 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.debate_ournament.users.controller.AuthController#register(Object, HttpServletRequest)
2025-05-26 22:59:38.183 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{username=cleanloguser, email=<EMAIL>, password=password123, confirmPassword=passwo (truncated)...]
2025-05-26 22:59:38.238 [http-nio-8088-exec-5] INFO  c.d.users.service.AuthService - 用户注册请求: username=cleanloguser, email=<EMAIL>, ip=127.0.0.1
2025-05-26 22:59:38.239 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证验证码: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC, inputCode=TCRJ
2025-05-26 22:59:38.243 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used
    from
        captchas c1_0
    where
        c1_0.session_id=?
        and c1_0.used=0
        and c1_0.expires_at>?
2025-05-26 22:59:38.249 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证码验证成功: sessionId=A9145D07CDEE3B0D22E9D7975F1379DC
2025-05-26 22:59:38.250 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 创建新用户: username=cleanloguser, email=<EMAIL>
2025-05-26 22:59:38.256 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    select
        u1_0.id
    from
        users u1_0
    where
        u1_0.username=?
    limit
        ?
2025-05-26 22:59:38.259 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    select
        u1_0.id
    from
        users u1_0
    where
        u1_0.email=?
    limit
        ?
2025-05-26 22:59:38.410 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    insert
    into
        users
        (created_at, email, email_verified, last_login_attempt, last_login_ip, last_login_time, locked_until, login_attempts, password, status, updated_at, username)
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:59:38.432 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    insert
    into
        user_levels
        (created_at, current_streak, experience, experience_to_next_level, highest_streak, level, total_debates, total_wins, updated_at, user_id, win_rate)
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:59:38.435 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户等级记录创建成功: userId=8
2025-05-26 22:59:38.441 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    insert
    into
        user_profiles
        (avatar_content_type, avatar_file_name, avatar_file_size, avatar_url, bio, birth_date, created_at, display_name, gender, is_profile_public, location, major_or_field, school_or_organization, show_email, show_real_name, updated_at, user_id)
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 22:59:38.444 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户资料记录创建成功: userId=8
2025-05-26 22:59:38.444 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户创建成功: userId=8
2025-05-26 22:59:38.444 [http-nio-8088-exec-5] INFO  c.d.users.service.AuthService - 用户注册成功: userId=8
2025-05-26 22:59:38.454 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    update
        captchas
    set
        attempt_count=?,
        client_ip=?,
        code=?,
        created_at=?,
        expires_at=?,
        used=?
    where
        session_id=?
2025-05-26 22:59:38.468 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-05-26 22:59:38.468 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ApiResponse{code=200, message='注册成功', data={message=注册成功，请验证邮箱后登录, userId=8, email=cleanloguser@exam (truncated)...]
2025-05-26 22:59:38.471 [http-nio-8088-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-05-26 23:00:21.680 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 24904 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:00:21.682 [main] DEBUG c.d.AiDebateTournamentPlatformApplication - Running with Spring Boot v3.5.0, Spring v6.2.7
2025-05-26 23:00:21.683 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:00:24.802 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:00:24.898 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3111 ms
2025-05-26 23:00:28.422 [main] DEBUG org.hibernate.SQL -
    alter table chat_messages
       modify column ai_provider enum ('ALIBABA','ALIBABA_BAILIAN','ANTHROPIC','BAIDU','DEEPSEEK','MOONSHOT','OPENAI','OPENROUTER','SILICONFLOW','TENCENT','ZHIPU')
2025-05-26 23:00:28.440 [main] DEBUG org.hibernate.SQL -
    alter table mindmap_responses
       modify column mindmap_type enum ('ARGUMENT','COMPARISON','CONCEPT','FLOWCHART','HIERARCHY','KNOWLEDGE','NETWORK','TIMELINE')
2025-05-26 23:00:28.454 [main] DEBUG org.hibernate.SQL -
    alter table mindmap_responses
       modify column status enum ('CANCELLED','FAILED','PENDING','SUCCESS')
2025-05-26 23:00:28.470 [main] DEBUG org.hibernate.SQL -
    alter table user_levels
       modify column win_rate decimal(5,2) DEFAULT 0.00 COMMENT '胜率（百分比）' not null
2025-05-26 23:00:32.405 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-05-26 23:00:33.215 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:00:33.218 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复公钥
2025-05-26 23:00:33.219 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复私钥
2025-05-26 23:00:33.220 [main] DEBUG c.d.service.KeyManagementService - 成功加载现有密钥对
2025-05-26 23:00:33.220 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:00:33.871 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 109 mappings in 'requestMappingHandlerMapping'
2025-05-26 23:00:33.935 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /uploads/**, /avatars/**, /static/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-05-26 23:00:34.132 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:00:34.200 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name customUserDetailsService
2025-05-26 23:00:34.350 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter$$SpringCGLIB$$0, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-05-26 23:00:34.670 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-26 23:00:34.737 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-26 23:00:35.273 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-26 23:00:35.319 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:00:35.337 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

2025-05-26 23:01:21.706 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 27864 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:01:21.708 [main] DEBUG c.d.AiDebateTournamentPlatformApplication - Running with Spring Boot v3.5.0, Spring v6.2.7
2025-05-26 23:01:21.710 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:01:24.800 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:01:24.896 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3094 ms
2025-05-26 23:01:28.131 [main] DEBUG org.hibernate.SQL -
    alter table chat_messages
       modify column ai_provider enum ('ALIBABA','ALIBABA_BAILIAN','ANTHROPIC','BAIDU','DEEPSEEK','MOONSHOT','OPENAI','OPENROUTER','SILICONFLOW','TENCENT','ZHIPU')
2025-05-26 23:01:28.148 [main] DEBUG org.hibernate.SQL -
    alter table mindmap_responses
       modify column mindmap_type enum ('ARGUMENT','COMPARISON','CONCEPT','FLOWCHART','HIERARCHY','KNOWLEDGE','NETWORK','TIMELINE')
2025-05-26 23:01:28.162 [main] DEBUG org.hibernate.SQL -
    alter table mindmap_responses
       modify column status enum ('CANCELLED','FAILED','PENDING','SUCCESS')
2025-05-26 23:01:28.181 [main] DEBUG org.hibernate.SQL -
    alter table user_levels
       modify column win_rate decimal(5,2) DEFAULT 0.00 COMMENT '胜率（百分比）' not null
2025-05-26 23:01:31.798 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-05-26 23:01:32.576 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:01:32.580 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复公钥
2025-05-26 23:01:32.581 [main] DEBUG c.d.util.EncryptionUtil - 成功从Base64字符串恢复私钥
2025-05-26 23:01:32.581 [main] DEBUG c.d.service.KeyManagementService - 成功加载现有密钥对
2025-05-26 23:01:32.581 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:01:33.165 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 109 mappings in 'requestMappingHandlerMapping'
2025-05-26 23:01:33.226 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /uploads/**, /avatars/**, /static/**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-05-26 23:01:33.433 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:01:33.493 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name customUserDetailsService
2025-05-26 23:01:33.649 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter$$SpringCGLIB$$0, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-05-26 23:01:34.007 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-05-26 23:01:34.072 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice
2025-05-26 23:01:34.591 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 23:01:34.612 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 13.794 seconds (process running for 14.356)
2025-05-26 23:01:34.703 [main] DEBUG c.d.config.ApplicationStartupConfig - 主上传目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads
2025-05-26 23:01:34.703 [main] DEBUG c.d.config.ApplicationStartupConfig - 头像上传目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\avatars
2025-05-26 23:01:34.704 [main] DEBUG c.d.config.ApplicationStartupConfig - 临时文件目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\temp
2025-05-26 23:01:34.704 [main] DEBUG c.d.config.ApplicationStartupConfig - 备份目录已存在：E:\code\java上机\AI Debate Tournament Platform\uploads\backup
2025-05-26 23:01:34.704 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 23:01:34.704 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:01:34.727 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 23:01:34.730 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 6, 活跃用户数: 6
2025-05-26 23:02:09.169 [http-nio-8088-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-26 23:02:09.170 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-05-26 23:02:09.170 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-05-26 23:02:09.170 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-05-26 23:02:09.172 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@7d9bc433
2025-05-26 23:02:09.173 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@3bce28da
2025-05-26 23:02:09.173 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-05-26 23:02:09.173 [http-nio-8088-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-05-26 23:02:09.203 [http-nio-8088-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /auth/captcha/generate
2025-05-26 23:02:09.219 [http-nio-8088-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-05-26 23:02:09.227 [http-nio-8088-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /auth/captcha/generate
2025-05-26 23:02:09.230 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/api/auth/captcha/generate", parameters={}
2025-05-26 23:02:09.232 [http-nio-8088-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.debate_ournament.users.controller.CaptchaController#generateCaptcha(HttpServletRequest, HttpSession)
2025-05-26 23:02:09.278 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=CA784D9E8FCF5F7136CCBC6368ADF1A6, clientIp=127.0.0.1
2025-05-26 23:02:09.278 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:02:09.334 [http-nio-8088-exec-1] DEBUG org.hibernate.SQL -
    delete c1_0
    from
        captchas c1_0
    where
        c1_0.expires_at<?
2025-05-26 23:02:09.377 [http-nio-8088-exec-1] DEBUG org.hibernate.SQL -
    select
        count(c1_0.session_id)
    from
        captchas c1_0
    where
        c1_0.client_ip=?
        and c1_0.created_at>?
2025-05-26 23:02:09.410 [http-nio-8088-exec-1] DEBUG org.hibernate.SQL -
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used
    from
        captchas c1_0
    where
        c1_0.session_id=?
2025-05-26 23:02:09.603 [http-nio-8088-exec-1] DEBUG org.hibernate.SQL -
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used
    from
        captchas c1_0
    where
        c1_0.session_id=?
2025-05-26 23:02:09.620 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=CA784D9E8FCF5F7136CCBC6368ADF1A6, code=PTWC
2025-05-26 23:02:09.655 [http-nio-8088-exec-1] DEBUG org.hibernate.SQL -
    insert
    into
        captchas
        (attempt_count, client_ip, code, created_at, expires_at, used, session_id)
    values
        (?, ?, ?, ?, ?, ?, ?)
2025-05-26 23:02:09.727 [http-nio-8088-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-05-26 23:02:09.729 [http-nio-8088-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ApiResponse{code=200, message='验证码生成成功', data={sessionId=CA784D9E8FCF5F7136CCBC6368ADF1A6, message=验 (truncated)...]
2025-05-26 23:02:09.752 [http-nio-8088-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-05-26 23:02:37.158 [http-nio-8088-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /auth/register
2025-05-26 23:02:37.159 [http-nio-8088-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-05-26 23:02:37.160 [http-nio-8088-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /auth/register
2025-05-26 23:02:37.160 [http-nio-8088-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - POST "/api/auth/register", parameters={}
2025-05-26 23:02:37.160 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.debate_ournament.users.controller.AuthController#register(Object, HttpServletRequest)
2025-05-26 23:02:37.190 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Read "application/json;charset=UTF-8" to [{username=optimizeduser, email=<EMAIL>, password=password123, confirmPassword=pass (truncated)...]
2025-05-26 23:02:37.247 [http-nio-8088-exec-5] INFO  c.d.users.service.AuthService - 用户注册请求: username=optimizeduser, email=<EMAIL>, ip=127.0.0.1
2025-05-26 23:02:37.248 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证验证码: sessionId=CA784D9E8FCF5F7136CCBC6368ADF1A6, inputCode=PTWC
2025-05-26 23:02:37.253 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    select
        c1_0.session_id,
        c1_0.attempt_count,
        c1_0.client_ip,
        c1_0.code,
        c1_0.created_at,
        c1_0.expires_at,
        c1_0.used
    from
        captchas c1_0
    where
        c1_0.session_id=?
        and c1_0.used=0
        and c1_0.expires_at>?
2025-05-26 23:02:37.258 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证码验证成功: sessionId=CA784D9E8FCF5F7136CCBC6368ADF1A6
2025-05-26 23:02:37.259 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 创建新用户: username=optimizeduser, email=<EMAIL>
2025-05-26 23:02:37.264 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    select
        u1_0.id
    from
        users u1_0
    where
        u1_0.username=?
    limit
        ?
2025-05-26 23:02:37.267 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    select
        u1_0.id
    from
        users u1_0
    where
        u1_0.email=?
    limit
        ?
2025-05-26 23:02:37.423 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    insert
    into
        users
        (created_at, email, email_verified, last_login_attempt, last_login_ip, last_login_time, locked_until, login_attempts, password, status, updated_at, username)
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 23:02:37.443 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    insert
    into
        user_levels
        (created_at, current_streak, experience, experience_to_next_level, highest_streak, level, total_debates, total_wins, updated_at, user_id, win_rate)
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 23:02:37.446 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户等级记录创建成功: userId=9
2025-05-26 23:02:37.452 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    insert
    into
        user_profiles
        (avatar_content_type, avatar_file_name, avatar_file_size, avatar_url, bio, birth_date, created_at, display_name, gender, is_profile_public, location, major_or_field, school_or_organization, show_email, show_real_name, updated_at, user_id)
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-05-26 23:02:37.455 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户资料记录创建成功: userId=9
2025-05-26 23:02:37.455 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户创建成功: userId=9
2025-05-26 23:02:37.455 [http-nio-8088-exec-5] INFO  c.d.users.service.AuthService - 用户注册成功: userId=9
2025-05-26 23:02:37.464 [http-nio-8088-exec-5] DEBUG org.hibernate.SQL -
    update
        captchas
    set
        attempt_count=?,
        client_ip=?,
        code=?,
        created_at=?,
        expires_at=?,
        used=?
    where
        session_id=?
2025-05-26 23:02:37.476 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/yaml]
2025-05-26 23:02:37.477 [http-nio-8088-exec-5] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [ApiResponse{code=200, message='注册成功', data={message=注册成功，请验证邮箱后登录, userId=9, email=optimizeduser@exa (truncated)...]
2025-05-26 23:02:37.479 [http-nio-8088-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-05-26 23:08:09.108 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 24092 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:08:09.110 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:08:10.980 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:08:11.045 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1867 ms
2025-05-26 23:08:12.790 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Unsupported character encoding 'utf8mb4'
2025-05-26 23:08:16.575 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:08:16.577 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:08:17.096 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:08:17.685 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-26 23:08:17.709 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:08:17.721 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

2025-05-26 23:09:30.565 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 13448 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:09:30.566 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:09:32.314 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:09:32.368 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1745 ms
2025-05-26 23:09:34.555 [main] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Access denied for user 'root'@'localhost' (using password: YES)
2025-05-26 23:09:38.231 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:09:38.233 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:09:38.709 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:09:39.306 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 23:09:39.322 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 9.318 seconds (process running for 9.664)
2025-05-26 23:09:39.383 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:09:40.392 [main] WARN  c.d.c.DatabaseInitializationConfig - 检查表 'users' 是否存在时发生错误: Access denied for user 'root'@'localhost' (using password: YES)
2025-05-26 23:09:40.392 [main] INFO  c.d.c.DatabaseInitializationConfig - 检测到表 'users' 不存在，需要初始化数据库
2025-05-26 23:09:40.392 [main] INFO  c.d.c.DatabaseInitializationConfig - 检测到数据库需要初始化，开始执行初始化脚本...
2025-05-26 23:09:40.392 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始执行数据库初始化脚本: db/init-database-merged.sql
2025-05-26 23:09:41.403 [main] ERROR c.d.c.DatabaseInitializationConfig - 执行数据库初始化脚本时发生错误
org.springframework.jdbc.datasource.init.UncategorizedScriptException: Failed to execute database script
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:67)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.debate_ournament.config.DatabaseInitializationConfig.initializeDatabase(DatabaseInitializationConfig.java:163)
	at com.debate_ournament.config.DatabaseInitializationConfig.lambda$databaseInitializer$0(DatabaseInitializationConfig.java:68)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:186)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:571)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:153)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:176)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:636)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.debate_ournament.AiDebateTournamentPlatformApplication.main(AiDebateTournamentPlatformApplication.java:25)
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:52)
	... 23 common frames omitted
Caused by: java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:837)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:139)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:368)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:205)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:483)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:101)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 24 common frames omitted
2025-05-26 23:09:41.405 [main] ERROR c.d.c.DatabaseInitializationConfig - 数据库初始化过程中发生错误: 数据库初始化失败
java.lang.RuntimeException: 数据库初始化失败
	at com.debate_ournament.config.DatabaseInitializationConfig.initializeDatabase(DatabaseInitializationConfig.java:172)
	at com.debate_ournament.config.DatabaseInitializationConfig.lambda$databaseInitializer$0(DatabaseInitializationConfig.java:68)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:186)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:571)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:153)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:176)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:636)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.debate_ournament.AiDebateTournamentPlatformApplication.main(AiDebateTournamentPlatformApplication.java:25)
Caused by: org.springframework.jdbc.datasource.init.UncategorizedScriptException: Failed to execute database script
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:67)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.debate_ournament.config.DatabaseInitializationConfig.initializeDatabase(DatabaseInitializationConfig.java:163)
	... 21 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:52)
	... 23 common frames omitted
Caused by: java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:837)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:139)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:368)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:205)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:483)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:101)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 24 common frames omitted
2025-05-26 23:09:41.407 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:09:41.419 [main] ERROR o.s.boot.SpringApplication - Application run failed
java.lang.RuntimeException: 数据库初始化失败，应用启动中止
	at com.debate_ournament.config.DatabaseInitializationConfig.lambda$databaseInitializer$0(DatabaseInitializationConfig.java:79)
	at org.springframework.boot.SpringApplication.lambda$callRunner$5(SpringApplication.java:789)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:797)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:788)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:773)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:186)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:571)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:153)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:176)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:636)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:773)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1362)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1351)
	at com.debate_ournament.AiDebateTournamentPlatformApplication.main(AiDebateTournamentPlatformApplication.java:25)
Caused by: java.lang.RuntimeException: 数据库初始化失败
	at com.debate_ournament.config.DatabaseInitializationConfig.initializeDatabase(DatabaseInitializationConfig.java:172)
	at com.debate_ournament.config.DatabaseInitializationConfig.lambda$databaseInitializer$0(DatabaseInitializationConfig.java:68)
	... 20 common frames omitted
Caused by: org.springframework.jdbc.datasource.init.UncategorizedScriptException: Failed to execute database script
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:67)
	at org.springframework.jdbc.datasource.init.ResourceDatabasePopulator.execute(ResourceDatabasePopulator.java:269)
	at com.debate_ournament.config.DatabaseInitializationConfig.initializeDatabase(DatabaseInitializationConfig.java:163)
	... 21 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)
	at org.springframework.jdbc.datasource.init.DatabasePopulatorUtils.execute(DatabasePopulatorUtils.java:52)
	... 23 common frames omitted
Caused by: java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:837)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:420)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:238)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:180)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:139)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:368)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:205)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:483)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:101)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)
	... 24 common frames omitted
2025-05-26 23:09:41.423 [main] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-05-26 23:09:41.443 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-05-26 23:10:13.462 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 16564 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:10:13.463 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:10:15.239 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:10:15.299 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1776 ms
2025-05-26 23:10:19.882 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:10:19.885 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:10:20.532 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:10:21.241 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 23:10:21.256 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 8.371 seconds (process running for 8.698)
2025-05-26 23:10:21.324 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:10:21.351 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 23:10:21.353 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 7, 活跃用户数: 7
2025-05-26 23:10:21.353 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 23:10:47.336 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 25472 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:10:47.338 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:10:50.192 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:10:50.281 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2851 ms
2025-05-26 23:10:56.198 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:10:56.235 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:10:57.361 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:10:58.630 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-26 23:10:58.702 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:10:58.721 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

2025-05-26 23:11:03.563 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=3A228D9A1BE030C53A857EB0753D829A, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:11:03.564 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:11:03.851 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=3A228D9A1BE030C53A857EB0753D829A, code=DY93
2025-05-26 23:11:07.157 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=80DC4D22FFDF3625CEEC1F66EBC6775D, clientIp=127.0.0.1
2025-05-26 23:11:07.157 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:11:07.170 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=80DC4D22FFDF3625CEEC1F66EBC6775D, code=TS6T
2025-05-26 23:11:19.871 [http-nio-8088-exec-10] INFO  c.d.users.service.AuthService - 用户登录请求: username=BaSui, ip=0:0:0:0:0:0:0:1
2025-05-26 23:11:19.871 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 验证验证码: sessionId=3A228D9A1BE030C53A857EB0753D829A, inputCode=DY93
2025-05-26 23:11:19.878 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 验证码验证成功: sessionId=3A228D9A1BE030C53A857EB0753D829A
2025-05-26 23:11:20.006 [http-nio-8088-exec-10] INFO  c.d.users.service.UserService - 用户登录成功: userId=6, ip=0:0:0:0:0:0:0:1
2025-05-26 23:11:20.039 [http-nio-8088-exec-10] INFO  c.d.users.service.AuthService - 用户登录成功: userId=6
2025-05-26 23:11:35.718 [http-nio-8088-exec-5] INFO  c.d.users.service.AuthService - 用户注册请求: username=mergedconfiguser, email=<EMAIL>, ip=127.0.0.1
2025-05-26 23:11:35.719 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证验证码: sessionId=80DC4D22FFDF3625CEEC1F66EBC6775D, inputCode=TS6T
2025-05-26 23:11:35.721 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证码验证成功: sessionId=80DC4D22FFDF3625CEEC1F66EBC6775D
2025-05-26 23:11:35.722 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 创建新用户: username=mergedconfiguser, email=<EMAIL>
2025-05-26 23:11:35.814 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户等级记录创建成功: userId=10
2025-05-26 23:11:35.820 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户资料记录创建成功: userId=10
2025-05-26 23:11:35.820 [http-nio-8088-exec-5] INFO  c.d.users.service.UserService - 用户创建成功: userId=10
2025-05-26 23:11:35.820 [http-nio-8088-exec-5] INFO  c.d.users.service.AuthService - 用户注册成功: userId=10
2025-05-26 23:12:28.936 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=20F760539B0052D3246C0EAA5BABB50E, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:12:28.936 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:12:28.953 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=20F760539B0052D3246C0EAA5BABB50E, code=LPQ7
2025-05-26 23:12:57.227 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=1721D6526D73BE4F391769526AB5B266, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:12:57.227 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:12:57.241 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=1721D6526D73BE4F391769526AB5B266, code=VBK4
2025-05-26 23:13:09.534 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=74CEEE28E2FF857391027D7E7E5EEE50, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:13:09.534 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:13:09.544 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=74CEEE28E2FF857391027D7E7E5EEE50, code=ZD3R
2025-05-26 23:13:20.510 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=2F09EF2AA77AF654225ADEFED85E4170, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:13:20.510 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:13:20.521 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=2F09EF2AA77AF654225ADEFED85E4170, code=4FLQ
2025-05-26 23:13:26.533 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=3BA146BC10909FC4114C5E3E019A4A77, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:13:26.533 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:13:26.548 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=3BA146BC10909FC4114C5E3E019A4A77, code=WU7F
2025-05-26 23:13:47.269 [http-nio-8088-exec-5] ERROR com.debate_ournament.util.JwtUtil - JWT解析失败: JWT strings must contain exactly 2 period characters. Found: 0
2025-05-26 23:13:47.270 [http-nio-8088-exec-5] ERROR c.d.security.JwtAuthenticationFilter - JWT认证过滤器异常: JWT strings must contain exactly 2 period characters. Found: 0
io.jsonwebtoken.MalformedJwtException: JWT strings must contain exactly 2 period characters. Found: 0
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:275)
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529)
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589)
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)
	at com.debate_ournament.util.JwtUtil.extractAllClaims(JwtUtil.java:120)
	at com.debate_ournament.util.JwtUtil.extractClaim(JwtUtil.java:108)
	at com.debate_ournament.util.JwtUtil.extractUsername(JwtUtil.java:80)
	at com.debate_ournament.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.debate_ournament.security.JwtAuthenticationFilter$$SpringCGLIB$$0.doFilterInternal(<generated>)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:13:47.300 [http-nio-8088-exec-1] ERROR com.debate_ournament.util.JwtUtil - JWT解析失败: JWT strings must contain exactly 2 period characters. Found: 0
2025-05-26 23:13:47.300 [http-nio-8088-exec-1] ERROR c.d.security.JwtAuthenticationFilter - JWT认证过滤器异常: JWT strings must contain exactly 2 period characters. Found: 0
io.jsonwebtoken.MalformedJwtException: JWT strings must contain exactly 2 period characters. Found: 0
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:275)
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529)
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589)
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)
	at com.debate_ournament.util.JwtUtil.extractAllClaims(JwtUtil.java:120)
	at com.debate_ournament.util.JwtUtil.extractClaim(JwtUtil.java:108)
	at com.debate_ournament.util.JwtUtil.extractUsername(JwtUtil.java:80)
	at com.debate_ournament.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.debate_ournament.security.JwtAuthenticationFilter$$SpringCGLIB$$0.doFilterInternal(<generated>)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:13:56.187 [http-nio-8088-exec-3] ERROR com.debate_ournament.util.JwtUtil - JWT解析失败: JWT strings must contain exactly 2 period characters. Found: 0
2025-05-26 23:13:56.187 [http-nio-8088-exec-3] ERROR c.d.security.JwtAuthenticationFilter - JWT认证过滤器异常: JWT strings must contain exactly 2 period characters. Found: 0
io.jsonwebtoken.MalformedJwtException: JWT strings must contain exactly 2 period characters. Found: 0
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:275)
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529)
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589)
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)
	at com.debate_ournament.util.JwtUtil.extractAllClaims(JwtUtil.java:120)
	at com.debate_ournament.util.JwtUtil.extractClaim(JwtUtil.java:108)
	at com.debate_ournament.util.JwtUtil.extractUsername(JwtUtil.java:80)
	at com.debate_ournament.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.debate_ournament.security.JwtAuthenticationFilter$$SpringCGLIB$$0.doFilterInternal(<generated>)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:13:56.207 [http-nio-8088-exec-4] ERROR com.debate_ournament.util.JwtUtil - JWT解析失败: JWT strings must contain exactly 2 period characters. Found: 0
2025-05-26 23:13:56.207 [http-nio-8088-exec-4] ERROR c.d.security.JwtAuthenticationFilter - JWT认证过滤器异常: JWT strings must contain exactly 2 period characters. Found: 0
io.jsonwebtoken.MalformedJwtException: JWT strings must contain exactly 2 period characters. Found: 0
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:275)
	at io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529)
	at io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589)
	at io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)
	at com.debate_ournament.util.JwtUtil.extractAllClaims(JwtUtil.java:120)
	at com.debate_ournament.util.JwtUtil.extractClaim(JwtUtil.java:108)
	at com.debate_ournament.util.JwtUtil.extractUsername(JwtUtil.java:80)
	at com.debate_ournament.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.debate_ournament.security.JwtAuthenticationFilter$$SpringCGLIB$$0.doFilterInternal(<generated>)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:14:45.417 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=2A7C3C509A4782C55013B0C75CB306DE, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:14:45.417 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:14:45.428 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=2A7C3C509A4782C55013B0C75CB306DE, code=AG5J
2025-05-26 23:15:04.090 [http-nio-8088-exec-5] INFO  c.d.u.c.EncryptionController - 成功返回公钥信息，密钥ID: key_1748266619048_e04cbfdc
2025-05-26 23:16:52.765 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=C3856C18EF33D55E18E123C8FAA3C6B7, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:16:52.765 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:16:52.780 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=C3856C18EF33D55E18E123C8FAA3C6B7, code=TKWX
2025-05-26 23:17:13.702 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=A4D627399D2161FF040F82B5D1A9041F, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:17:13.702 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:17:13.714 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=A4D627399D2161FF040F82B5D1A9041F, code=7SK8
2025-05-26 23:17:34.704 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=F7002D385E2836E63185D11712E03604, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:17:34.704 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:17:34.717 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=F7002D385E2836E63185D11712E03604, code=9HVF
2025-05-26 23:17:57.031 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 刷新验证码: sessionId=F7002D385E2836E63185D11712E03604, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:17:57.031 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:17:57.041 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=F7002D385E2836E63185D11712E03604, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:17:57.041 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:17:57.061 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=F7002D385E2836E63185D11712E03604, code=LJ5Q
2025-05-26 23:18:00.419 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=7309E0D036AA98224F3E92BEE1A5C9E2, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:18:00.420 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:18:00.433 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=7309E0D036AA98224F3E92BEE1A5C9E2, code=36PE
2025-05-26 23:23:54.970 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 14296 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:23:54.972 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:23:57.693 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:23:57.794 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2743 ms
2025-05-26 23:24:02.760 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:24:02.979 [main] INFO  c.d.service.KeyManagementService - 密钥对已保存到文件系统，密钥ID: key_1748273042975_746215b4
2025-05-26 23:24:02.982 [main] INFO  c.d.service.KeyManagementService - 成功生成新密钥对，密钥ID: key_1748273042975_746215b4
2025-05-26 23:24:03.701 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:24:04.449 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 23:24:04.468 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 10.286 seconds (process running for 10.86)
2025-05-26 23:24:04.577 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 23:24:04.578 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:24:04.641 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 23:24:04.644 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 8, 活跃用户数: 8
2025-05-26 23:24:42.458 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=2D50F265629963B05382FE72FC60DF10, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:24:42.459 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:24:42.796 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=2D50F265629963B05382FE72FC60DF10, code=U7Z9
2025-05-26 23:25:05.797 [http-nio-8088-exec-5] INFO  c.d.u.c.EncryptionController - 成功返回公钥信息，密钥ID: key_1748273042975_746215b4
2025-05-26 23:25:05.845 [http-nio-8088-exec-10] ERROR c.d.exception.GlobalExceptionHandler - 未处理的异常: No static resource api/auth/login., 请求路径: uri=/api/api/auth/login
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource api/auth/login.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.debate_ournament.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:70)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.debate_ournament.security.JwtAuthenticationFilter$$SpringCGLIB$$0.doFilterInternal(<generated>)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:26:11.242 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 12372 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:26:11.244 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:26:12.570 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'characterEncodingFilter' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration; factoryMethodName=characterEncodingFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.class]] for bean 'characterEncodingFilter' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=encodingConfig; factoryMethodName=characterEncodingFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/debate_ournament/config/EncodingConfig.class]] bound.
2025-05-26 23:26:12.581 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:26:12.598 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'characterEncodingFilter', defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.class], could not be registered. A bean with that name has already been defined in class path resource [com/debate_ournament/config/EncodingConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

2025-05-26 23:27:07.568 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 11424 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:27:07.569 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:27:09.382 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'characterEncodingFilter' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.class]: Cannot register bean definition [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration; factoryMethodName=characterEncodingFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.class]] for bean 'characterEncodingFilter' since there is already [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=encodingConfig; factoryMethodName=characterEncodingFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/debate_ournament/config/EncodingConfig.class]] bound.
2025-05-26 23:27:09.402 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:27:09.427 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'characterEncodingFilter', defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/HttpEncodingAutoConfiguration.class], could not be registered. A bean with that name has already been defined in class path resource [com/debate_ournament/config/EncodingConfig.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

2025-05-26 23:27:40.182 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 9328 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:27:40.184 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:27:43.167 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:27:43.242 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2952 ms
2025-05-26 23:27:48.572 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:27:48.577 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273042975_746215b4
2025-05-26 23:27:49.206 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:27:49.866 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 23:27:49.885 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 10.882 seconds (process running for 11.527)
2025-05-26 23:27:49.949 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:27:49.980 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 23:27:49.981 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 8, 活跃用户数: 8
2025-05-26 23:27:49.982 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 23:28:21.910 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=B6ACC0F5A31B9573FEFB145643BE35EC, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:28:21.910 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:28:22.215 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=B6ACC0F5A31B9573FEFB145643BE35EC, code=C74Z
2025-05-26 23:28:55.559 [http-nio-8088-exec-9] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=D9BD11A3B981518B6B206635CEC737B5, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:28:55.559 [http-nio-8088-exec-9] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:28:55.573 [http-nio-8088-exec-9] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=D9BD11A3B981518B6B206635CEC737B5, code=ZL3R
2025-05-26 23:29:14.156 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=5A217C423D4A417B5464EAFE7389E1E4, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:29:14.156 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:29:14.168 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=5A217C423D4A417B5464EAFE7389E1E4, code=6YJ5
2025-05-26 23:29:27.680 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=BB6210FB6C16468419E36746B9FFEA07, clientIp=127.0.0.1
2025-05-26 23:29:27.680 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:29:27.690 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=BB6210FB6C16468419E36746B9FFEA07, code=L7PJ
2025-05-26 23:29:48.896 [http-nio-8088-exec-3] INFO  c.d.users.service.AuthService - 用户登录请求: username=BaSui, ip=127.0.0.1
2025-05-26 23:29:48.897 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 验证验证码: sessionId=BB6210FB6C16468419E36746B9FFEA07, inputCode=L7PJ
2025-05-26 23:29:48.901 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 验证码验证成功: sessionId=BB6210FB6C16468419E36746B9FFEA07
2025-05-26 23:29:48.997 [http-nio-8088-exec-3] WARN  c.d.users.service.AuthService - 密码错误: userId=6
2025-05-26 23:29:48.997 [http-nio-8088-exec-3] WARN  c.d.users.service.UserService - 用户登录失败: userId=6, attempts=1
2025-05-26 23:30:09.568 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=30D95C894D803CC28FB1C0E744E2F26B, clientIp=127.0.0.1
2025-05-26 23:30:09.569 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:30:09.577 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=30D95C894D803CC28FB1C0E744E2F26B, code=BSST
2025-05-26 23:30:30.659 [http-nio-8088-exec-6] INFO  c.d.users.service.AuthService - 用户登录请求: username=mergedconfiguser, ip=127.0.0.1
2025-05-26 23:30:30.659 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 验证验证码: sessionId=30D95C894D803CC28FB1C0E744E2F26B, inputCode=BSST
2025-05-26 23:30:30.661 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 验证码验证成功: sessionId=30D95C894D803CC28FB1C0E744E2F26B
2025-05-26 23:30:30.662 [http-nio-8088-exec-6] WARN  c.d.users.service.AuthService - 用户不存在: username=mergedconfiguser
2025-05-26 23:30:53.006 [http-nio-8088-exec-7] INFO  c.d.u.c.EncryptionController - 成功返回公钥信息，密钥ID: key_1748273042975_746215b4
2025-05-26 23:30:53.033 [http-nio-8088-exec-8] ERROR c.d.exception.GlobalExceptionHandler - 未处理的异常: No static resource api/auth/login., 请求路径: uri=/api/api/auth/login
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource api/auth/login.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.debate_ournament.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:70)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.debate_ournament.security.JwtAuthenticationFilter$$SpringCGLIB$$0.doFilterInternal(<generated>)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:32:34.453 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=F2BDEA451D19E1DA522E320312D1891C, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:32:34.454 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:32:34.467 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=F2BDEA451D19E1DA522E320312D1891C, code=3CYR
2025-05-26 23:32:34.492 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=2BC9589D64C3CFE8949ADF8E66F6B8F8, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:32:34.492 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:32:34.507 [http-nio-8088-exec-10] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=2BC9589D64C3CFE8949ADF8E66F6B8F8, code=986H
2025-05-26 23:32:46.106 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 7740 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:32:46.108 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:32:47.814 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:32:47.871 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1713 ms
2025-05-26 23:32:52.217 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:32:52.221 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273042975_746215b4
2025-05-26 23:32:52.731 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:32:53.383 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-26 23:32:53.420 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:32:53.432 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

2025-05-26 23:34:03.051 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=97D9895B3AF3223C4E97DE4A4EEC1553, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:34:03.051 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:34:03.063 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=97D9895B3AF3223C4E97DE4A4EEC1553, code=WVKD
2025-05-26 23:34:03.249 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=62B3ECB103DBC332E83C3D06D7689F3D, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:34:03.249 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:34:03.258 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=62B3ECB103DBC332E83C3D06D7689F3D, code=S5AQ
2025-05-26 23:34:06.935 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=CCE35A6E7A7ED2D306F05DFAC39BA837, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:34:06.936 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:34:06.947 [http-nio-8088-exec-1] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=CCE35A6E7A7ED2D306F05DFAC39BA837, code=RKSG
2025-05-26 23:34:18.235 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=0539C1B194EB200EEE1167704BF57AB5, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:34:18.235 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:34:18.248 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=0539C1B194EB200EEE1167704BF57AB5, code=XD8F
2025-05-26 23:34:23.842 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=928A7D64F708A3AC69B4DDD8B01D0F0B, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:34:23.842 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:34:23.852 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=928A7D64F708A3AC69B4DDD8B01D0F0B, code=PPG9
2025-05-26 23:35:23.691 [http-nio-8088-exec-5] ERROR c.d.exception.GlobalExceptionHandler - 未处理的异常: Request method 'POST' is not supported, 请求路径: uri=/api/auth/captcha/generate
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:267)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:127)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:509)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1284)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:38:07.346 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 21756 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:38:07.348 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:38:09.870 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:38:09.931 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2499 ms
2025-05-26 23:38:14.446 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:38:14.933 [main] INFO  c.d.service.KeyManagementService - 密钥对已保存到文件系统，密钥ID: key_1748273894930_780d745d
2025-05-26 23:38:14.934 [main] INFO  c.d.service.KeyManagementService - 成功生成新密钥对，密钥ID: key_1748273894930_780d745d
2025-05-26 23:38:15.426 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:38:16.018 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 23:38:16.033 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 9.522 seconds (process running for 10.026)
2025-05-26 23:38:16.109 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 23:38:16.110 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:38:16.136 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 23:38:16.137 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 1, 活跃用户数: 1
2025-05-26 23:40:27.115 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=3AF1F6A128AFEDE8E49331C925547D51, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:40:27.115 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:40:27.367 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=3AF1F6A128AFEDE8E49331C925547D51, code=ENRQ
2025-05-26 23:40:38.546 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=26428B1A10ACDADE068774421AA7C2F8, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:40:38.546 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:40:38.561 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=26428B1A10ACDADE068774421AA7C2F8, code=EW7H
2025-05-26 23:40:47.579 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=80E439EBD1270B43369D08B4E4A60B48, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:40:47.579 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:40:47.592 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=80E439EBD1270B43369D08B4E4A60B48, code=AFKQ
2025-05-26 23:42:34.677 [http-nio-8088-exec-1] INFO  c.d.u.c.EncryptionController - 成功返回公钥信息，密钥ID: key_1748273894930_780d745d
2025-05-26 23:42:34.715 [http-nio-8088-exec-3] ERROR c.d.exception.GlobalExceptionHandler - 未处理的异常: No static resource api/auth/login., 请求路径: uri=/api/api/auth/login
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource api/auth/login.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.debate_ournament.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:70)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:565)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724)
	at com.debate_ournament.security.JwtAuthenticationFilter$$SpringCGLIB$$0.doFilterInternal(<generated>)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
2025-05-26 23:42:58.479 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=4D009A8C1104AE320A5A753700101F52, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:42:58.479 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:42:58.490 [http-nio-8088-exec-7] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=4D009A8C1104AE320A5A753700101F52, code=UA5X
2025-05-26 23:43:06.362 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=E35B16259F5C1BA9E6C2DAFB7F736CE9, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:43:06.362 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:43:06.378 [http-nio-8088-exec-2] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=E35B16259F5C1BA9E6C2DAFB7F736CE9, code=5N7E
2025-05-26 23:46:21.684 [http-nio-8088-exec-1] INFO  c.d.u.c.EncryptionController - 成功返回公钥信息，密钥ID: key_1748273894930_780d745d
2025-05-26 23:46:49.660 [http-nio-8088-exec-4] ERROR c.d.exception.GlobalExceptionHandler - 运行时异常: JSON parse error: Unexpected character ('\' (code 92)): was expecting double-quote to start field name, 请求路径: uri=/api/auth/login
org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character ('\' (code 92)): was expecting double-quote to start field name
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:408)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.read(AbstractJackson2HttpMessageConverter.java:356)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodArgumentResolver.readWithMessageConverters(AbstractMessageConverterMethodArgumentResolver.java:204)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.readWithMessageConverters(RequestResponseBodyMethodProcessor.java:176)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.resolveArgument(RequestResponseBodyMethodProcessor.java:150)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:227)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:181)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:125)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:119)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.ServletRequestPathFilter.doFilter(ServletRequestPathFilter.java:52)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebSecurityConfiguration.java:319)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:240)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:114)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:116)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:398)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:903)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1740)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1447)
Caused by: com.fasterxml.jackson.core.JsonParseException: Unexpected character ('\' (code 92)): was expecting double-quote to start field name
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 2]
	at com.fasterxml.jackson.core.JsonParser._constructReadException(JsonParser.java:2666)
	at com.fasterxml.jackson.core.base.ParserMinimalBase._reportUnexpectedChar(ParserMinimalBase.java:742)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._handleOddName(UTF8StreamJsonParser.java:2103)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser._parseName(UTF8StreamJsonParser.java:1753)
	at com.fasterxml.jackson.core.json.UTF8StreamJsonParser.nextFieldName(UTF8StreamJsonParser.java:1040)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializerNR._deserializeNR(UntypedObjectDeserializerNR.java:223)
	at com.fasterxml.jackson.databind.deser.std.UntypedObjectDeserializerNR.deserialize(UntypedObjectDeserializerNR.java:69)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectReader._bindAndClose(ObjectReader.java:2130)
	at com.fasterxml.jackson.databind.ObjectReader.readValue(ObjectReader.java:1500)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.readJavaType(AbstractJackson2HttpMessageConverter.java:397)
	... 143 common frames omitted
2025-05-26 23:47:15.627 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=F7B15FBE8B598E4B96BA0275DFFA9922, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:47:15.627 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:47:15.639 [http-nio-8088-exec-3] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=F7B15FBE8B598E4B96BA0275DFFA9922, code=EAYC
2025-05-26 23:47:26.218 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=1E9916A0754EA6B17281E1512AD11127, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:47:26.218 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:47:26.231 [http-nio-8088-exec-6] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=1E9916A0754EA6B17281E1512AD11127, code=ATTF
2025-05-26 23:47:35.359 [http-nio-8088-exec-9] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=712DBB94B307648C1E9CEA558F451675, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:47:35.360 [http-nio-8088-exec-9] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:47:35.370 [http-nio-8088-exec-9] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=712DBB94B307648C1E9CEA558F451675, code=FP9E
2025-05-26 23:47:44.394 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=C847CC48A016865C161930F990665935, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:47:44.394 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:47:44.405 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=C847CC48A016865C161930F990665935, code=MTRT
2025-05-26 23:47:53.298 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=77482ABF600194915A148D342CE143E4, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:47:53.299 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:47:53.313 [http-nio-8088-exec-8] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=77482ABF600194915A148D342CE143E4, code=ZU6H
2025-05-26 23:48:01.303 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=866AA8006ABC280A6E689D1FFCA8EEE2, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:48:01.303 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:48:01.313 [http-nio-8088-exec-5] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=866AA8006ABC280A6E689D1FFCA8EEE2, code=DDVB
2025-05-26 23:50:27.982 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 19864 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:50:27.984 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:50:30.815 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:50:30.889 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2812 ms
2025-05-26 23:50:35.862 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:50:35.866 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273894930_780d745d
2025-05-26 23:50:36.527 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:50:37.319 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-26 23:50:37.357 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger -

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:50:37.372 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter -

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

2025-05-26 23:52:40.682 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=C453106A9F3446CC07BBDC1A0FBFB389, clientIp=0:0:0:0:0:0:0:1
2025-05-26 23:52:40.682 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:52:40.693 [http-nio-8088-exec-4] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=C453106A9F3446CC07BBDC1A0FBFB389, code=D267
2025-05-26 23:53:48.261 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 6148 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:53:48.262 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:53:50.112 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:53:50.182 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1861 ms
2025-05-26 23:53:54.458 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:53:54.461 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273894930_780d745d
2025-05-26 23:53:54.957 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:53:55.566 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8088 (http) with context path '/api'
2025-05-26 23:53:55.581 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 7.958 seconds (process running for 8.318)
2025-05-26 23:53:55.640 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 23:53:55.640 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:53:55.669 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 23:53:55.671 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 3, 活跃用户数: 3
2025-05-26 23:54:54.856 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 5688 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:54:54.857 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:54:57.601 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8088 (http)
2025-05-26 23:54:57.709 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2777 ms
2025-05-26 23:55:02.680 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:55:02.684 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273894930_780d745d
2025-05-26 23:55:03.343 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:55:04.290 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-26 23:55:04.335 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-26 23:55:04.354 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8088 was already in use.

Action:

Identify and stop the process that's listening on port 8088 or configure this application to listen on another port.

2025-05-26 23:56:40.394 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 21484 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-26 23:56:40.396 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 23:56:42.233 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-26 23:56:42.286 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1831 ms
2025-05-26 23:56:47.553 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-26 23:56:47.558 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273894930_780d745d
2025-05-26 23:56:48.247 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-26 23:56:49.029 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-05-26 23:56:49.045 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 9.209 seconds (process running for 9.536)
2025-05-26 23:56:49.129 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-26 23:56:49.160 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-26 23:56:49.162 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 3, 活跃用户数: 3
2025-05-26 23:56:49.163 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-26 23:59:18.446 [http-nio-8080-exec-4] INFO  c.d.users.service.CaptchaService - 生成验证码: sessionId=F1F3EE4CE10BC676790178E0628E9275, clientIp=127.0.0.1
2025-05-26 23:59:18.446 [http-nio-8080-exec-4] INFO  c.d.users.service.CaptchaService - 清理过期验证码
2025-05-26 23:59:18.675 [http-nio-8080-exec-4] INFO  c.d.users.service.CaptchaService - 验证码生成成功: sessionId=F1F3EE4CE10BC676790178E0628E9275, code=YCA5
2025-05-27 00:00:43.003 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 20136 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-27 00:00:43.005 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 00:00:47.461 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-27 00:00:47.625 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4518 ms
2025-05-27 00:00:47.720 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 7452 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-27 00:00:47.722 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 00:00:49.531 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-27 00:00:49.535 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-05-27 00:00:50.739 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-27 00:00:50.862 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2958 ms
2025-05-27 00:00:52.998 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-27 00:00:53.000 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-05-27 00:00:54.801 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-27 00:00:54.806 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273894930_780d745d
2025-05-27 00:00:55.580 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-27 00:00:56.572 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-27 00:00:56.622 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-27 00:00:56.651 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-05-27 00:00:57.525 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-27 00:00:57.528 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748273894930_780d745d
2025-05-27 00:00:58.177 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoints beneath base path '/actuator'
2025-05-27 00:00:59.052 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-27 00:00:59.093 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-27 00:00:59.110 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-05-27 00:01:47.456 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 16752 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-27 00:01:47.458 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 00:01:50.240 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-27 00:01:50.310 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2777 ms
2025-05-27 00:01:51.595 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-27 00:01:51.597 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-05-27 00:01:54.734 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-27 00:01:54.983 [main] INFO  c.d.service.KeyManagementService - 密钥对已保存到文件系统，密钥ID: key_1748275314979_8507b38a
2025-05-27 00:01:54.984 [main] INFO  c.d.service.KeyManagementService - 成功生成新密钥对，密钥ID: key_1748275314979_8507b38a
2025-05-27 00:01:55.819 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-05-27 00:01:56.710 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-27 00:01:56.745 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-27 00:01:56.760 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-05-27 00:03:27.863 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 21400 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-27 00:03:27.864 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 00:03:29.900 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-27 00:03:29.997 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2076 ms
2025-05-27 00:03:31.190 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-27 00:03:31.192 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-05-27 00:03:35.273 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-27 00:03:35.284 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748275314979_8507b38a
2025-05-27 00:03:36.571 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-05-27 00:03:38.442 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-05-27 00:03:38.474 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 11.119 seconds (process running for 11.434)
2025-05-27 00:03:38.567 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-27 00:03:38.567 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-27 00:03:38.608 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-27 00:03:38.610 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 3, 活跃用户数: 3
2025-05-27 00:05:40.969 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 7548 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-27 00:05:40.971 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 00:05:42.859 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-27 00:05:42.913 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1868 ms
2025-05-27 00:05:43.738 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-27 00:05:43.740 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-05-27 00:05:46.310 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-27 00:05:46.313 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748275314979_8507b38a
2025-05-27 00:05:47.006 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-05-27 00:05:47.845 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-27 00:05:47.874 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-27 00:05:47.886 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-05-27 00:08:12.546 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 3900 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-27 00:08:12.548 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 00:08:14.401 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-05-27 00:08:14.477 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1868 ms
2025-05-27 00:08:15.358 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-27 00:08:15.360 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-05-27 00:08:18.465 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-27 00:08:18.468 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748275314979_8507b38a
2025-05-27 00:08:19.224 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-05-27 00:08:20.130 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path '/api'
2025-05-27 00:08:20.144 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Started AiDebateTournamentPlatformApplication in 8.161 seconds (process running for 8.506)
2025-05-27 00:08:20.203 [main] INFO  c.d.c.DatabaseInitializationConfig - 开始检查数据库初始化状态...
2025-05-27 00:08:20.229 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库已存在必要的表结构，跳过初始化
2025-05-27 00:08:20.231 [main] INFO  c.d.c.DatabaseInitializationConfig - 数据库状态 - 总用户数: 3, 活跃用户数: 3
2025-05-27 00:08:20.231 [main] INFO  c.d.config.ApplicationStartupConfig - 文件上传目录结构初始化完成
2025-05-27 00:09:16.086 [main] INFO  c.d.AiDebateTournamentPlatformApplication - Starting AiDebateTournamentPlatformApplication using Java 24 with PID 12796 (E:\code\java上机\AI Debate Tournament Platform\target\classes started by Administrator in E:\code\java上机\AI Debate Tournament Platform)
2025-05-27 00:09:16.088 [main] INFO  c.d.AiDebateTournamentPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-27 00:09:18.340 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-05-27 00:09:18.403 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2250 ms
2025-05-27 00:09:19.370 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQL8Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-05-27 00:09:19.372 [main] WARN  org.hibernate.orm.deprecation - HHH90000026: MySQL8Dialect has been deprecated; use org.hibernate.dialect.MySQLDialect instead
2025-05-27 00:09:22.379 [main] INFO  c.d.service.KeyManagementService - 开始初始化密钥管理服务
2025-05-27 00:09:22.382 [main] INFO  c.d.service.KeyManagementService - 成功加载现有密钥对，密钥ID: key_1748275314979_8507b38a
2025-05-27 00:09:23.100 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 4 endpoint(s) beneath base path '/actuator'
2025-05-27 00:09:23.996 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-27 00:09:24.024 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-27 00:09:24.035 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

