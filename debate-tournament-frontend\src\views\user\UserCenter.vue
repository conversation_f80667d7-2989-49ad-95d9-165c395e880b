<template>
  <div class="user-center">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">个人中心</h1>
      </div>

      <div class="profile-container">
        <div class="row">
          <!-- 左侧用户信息 -->
          <div class="col-md-4">
            <div class="card user-profile">
              <div class="user-profile__header">
                <div class="user-profile__avatar">
                  <img :src="user.avatar || 'http://placehold.co/150'" :alt="user.username">
                </div>
                <h2 class="user-profile__name">{{ user.username }}</h2>
                <p class="user-profile__email">{{ user.email }}</p>
                <div class="user-profile__level">
                  <span class="level-label">辩论等级</span>
                  <span class="level-badge">{{ user.level || 'Lv.1' }}</span>
                </div>
              </div>

              <div class="user-profile__stats">
                <div class="stat-item">
                  <span class="stat-value">{{ user.debateCount || 0 }}</span>
                  <span class="stat-label">参与辩论</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ user.winCount || 0 }}</span>
                  <span class="stat-label">获胜场次</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ user.pointsEarned || 0 }}</span>
                  <span class="stat-label">积分</span>
                </div>
              </div>

              <div class="user-profile__actions">
                <button class="btn btn--primary" @click="showEditProfile = true">
                  编辑资料
                </button>
              </div>
            </div>

            <div class="card side-nav">
              <div
                class="side-nav__item"
                v-for="tab in tabs"
                :key="tab.id"
                :class="{ 'active': activeTab === tab.id }"
                @click="activeTab = tab.id"
              >
                <span class="side-nav__icon">{{ tab.icon }}</span>
                <span class="side-nav__text">{{ tab.name }}</span>
              </div>
            </div>
          </div>

          <!-- 右侧内容 -->
          <div class="col-md-8">
            <!-- 我的辩论 -->
            <div v-if="activeTab === 'debates'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">我的辩论</h2>
                <div class="section-actions">
                  <select v-model="debateFilter" class="form-select">
                    <option value="all">全部辩论</option>
                    <option value="active">进行中</option>
                    <option value="completed">已结束</option>
                  </select>
                </div>
              </div>

              <div class="debate-list" v-if="!loadingDebates && userDebates.length > 0">
                <div
                  v-for="debate in userDebates"
                  :key="debate.id"
                  class="debate-item card"
                  @click="navigateToDebate(debate.id)"
                >
                  <div class="debate-item__header">
                    <h3 class="debate-item__title">{{ debate.title }}</h3>
                    <span
                      class="debate-status"
                      :class="`debate-status--${debate.status}`"
                    >
                      {{ getStatusText(debate.status) }}
                    </span>
                  </div>

                  <div class="debate-item__content">
                    <p class="debate-item__description">{{ debate.description }}</p>
                    <div class="debate-item__meta">
                      <span class="meta-item">
                        <span class="meta-icon">👥</span>
                        {{ debate.participantCount || 0 }} 参与者
                      </span>
                      <span class="meta-item">
                        <span class="meta-icon">💬</span>
                        {{ debate.speechCount || 0 }} 发言
                      </span>
                    </div>
                  </div>

                  <div class="debate-item__footer">
                    <span class="debate-item__time">{{ formatTime(debate.createTime) }}</span>
                    <span
                      class="debate-item__role"
                      :class="`role--${debate.userRole}`"
                    >
                      {{ getRoleText(debate.userRole) }}
                    </span>
                  </div>
                </div>
              </div>

              <div v-else-if="loadingDebates" class="loading-container">
                <div class="spinner"></div>
                <p>加载中...</p>
              </div>

              <div v-else class="empty-state">
                <p>您还没有参与任何辩论</p>
                <router-link to="/debate-hall" class="btn btn--primary">
                  前往辩论大厅
                </router-link>
              </div>
            </div>

            <!-- 收藏的辩论 -->
            <div v-if="activeTab === 'favorites'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">收藏的辩论</h2>
              </div>

              <div v-if="!loadingFavorites && favorites.length > 0" class="debate-list">
                <div
                  v-for="debate in favorites"
                  :key="debate.id"
                  class="debate-item card"
                  @click="navigateToDebate(debate.id)"
                >
                  <!-- 与上面类似的内容 -->
                </div>
              </div>

              <div v-else-if="loadingFavorites" class="loading-container">
                <div class="spinner"></div>
                <p>加载中...</p>
              </div>

              <div v-else class="empty-state">
                <p>您还没有收藏任何辩论</p>
                <router-link to="/debate-hall" class="btn btn--primary">
                  前往辩论大厅
                </router-link>
              </div>
            </div>

            <!-- 数据统计 -->
            <div v-if="activeTab === 'statistics'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">数据统计</h2>
              </div>

              <div class="statistics-container">
                <div class="card statistics-card">
                  <h3 class="statistics-card__title">辩论胜率</h3>
                  <div class="win-rate-chart" v-if="userData.totalDebates > 0">
                    <div class="win-rate-indicator">
                      <div class="win-rate-value">{{ Math.round(userData.winRate * 100) }}%</div>
                      <div class="win-rate-bar">
                        <div class="win-rate-fill" :style="{ width: `${userData.winRate * 100}%` }"></div>
                      </div>
                    </div>
                    <div class="win-rate-stats">
                      <div class="win-rate-stat">
                        <span class="stat-value win">{{ userData.wins }}</span>
                        <span class="stat-label">胜利</span>
                      </div>
                      <div class="win-rate-stat">
                        <span class="stat-value draw">{{ userData.draws }}</span>
                        <span class="stat-label">平局</span>
                      </div>
                      <div class="win-rate-stat">
                        <span class="stat-value loss">{{ userData.losses }}</span>
                        <span class="stat-label">失败</span>
                      </div>
                    </div>
                  </div>
                  <div class="empty-chart" v-else>
                    <p>暂无数据</p>
                  </div>
                </div>

                <div class="card statistics-card">
                  <h3 class="statistics-card__title">辩论主题偏好</h3>
                  <div class="topic-preferences" v-if="userData.topicPreferences.length > 0">
                    <div
                      v-for="topic in userData.topicPreferences"
                      :key="topic.name"
                      class="topic-preference"
                    >
                      <span class="topic-name">{{ topic.name }}</span>
                      <div class="topic-bar">
                        <div class="topic-fill" :style="{ width: `${topic.percentage}%` }"></div>
                      </div>
                      <span class="topic-percentage">{{ topic.percentage }}%</span>
                    </div>
                  </div>
                  <div class="empty-chart" v-else>
                    <p>暂无数据</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 账户设置 -->
            <div v-if="activeTab === 'settings'" class="content-section">
              <div class="section-header">
                <h2 class="section-title">账户设置</h2>
              </div>

              <div class="settings-container">
                <div class="card">
                  <h3 class="settings-section-title">修改密码</h3>
                  <form @submit.prevent="changePassword" class="form">
                    <div class="form-group">
                      <label class="form-label" for="currentPassword">当前密码</label>
                      <div class="password-input">
                        <input
                          :type="showCurrentPassword ? 'text' : 'password'"
                          id="currentPassword"
                          class="form-control"
                          v-model="passwordForm.currentPassword"
                          :class="{ 'is-invalid': passwordErrors.currentPassword }"
                          required
                        />
                        <button
                          type="button"
                          class="toggle-password-btn"
                          @click="showCurrentPassword = !showCurrentPassword"
                        >
                          {{ showCurrentPassword ? '隐藏' : '显示' }}
                        </button>
                      </div>
                      <span v-if="passwordErrors.currentPassword" class="form-text text-error">
                        {{ passwordErrors.currentPassword }}
                      </span>
                    </div>

                    <div class="form-group">
                      <label class="form-label" for="newPassword">新密码</label>
                      <div class="password-input">
                        <input
                          :type="showNewPassword ? 'text' : 'password'"
                          id="newPassword"
                          class="form-control"
                          v-model="passwordForm.newPassword"
                          :class="{ 'is-invalid': passwordErrors.newPassword }"
                          required
                        />
                        <button
                          type="button"
                          class="toggle-password-btn"
                          @click="showNewPassword = !showNewPassword"
                        >
                          {{ showNewPassword ? '隐藏' : '显示' }}
                        </button>
                      </div>
                      <span v-if="passwordErrors.newPassword" class="form-text text-error">
                        {{ passwordErrors.newPassword }}
                      </span>
                    </div>

                    <div class="form-group">
                      <label class="form-label" for="confirmPassword">确认新密码</label>
                      <div class="password-input">
                        <input
                          :type="showConfirmPassword ? 'text' : 'password'"
                          id="confirmPassword"
                          class="form-control"
                          v-model="passwordForm.confirmPassword"
                          :class="{ 'is-invalid': passwordErrors.confirmPassword }"
                          required
                        />
                        <button
                          type="button"
                          class="toggle-password-btn"
                          @click="showConfirmPassword = !showConfirmPassword"
                        >
                          {{ showConfirmPassword ? '隐藏' : '显示' }}
                        </button>
                      </div>
                      <span v-if="passwordErrors.confirmPassword" class="form-text text-error">
                        {{ passwordErrors.confirmPassword }}
                      </span>
                    </div>

                    <button
                      type="submit"
                      class="btn btn--primary"
                      :disabled="loadingPassword"
                    >
                      {{ loadingPassword ? '提交中...' : '修改密码' }}
                    </button>

                    <div v-if="passwordMessage" class="form-message" :class="passwordSuccess ? 'success' : 'error'">
                      {{ passwordMessage }}
                    </div>
                  </form>
                </div>

                <div class="card">
                  <h3 class="settings-section-title">隐私设置</h3>
                  <div class="settings-options">
                    <div class="settings-option">
                      <label class="switch">
                        <input type="checkbox" v-model="privacySettings.showEmail">
                        <span class="slider"></span>
                      </label>
                      <span>显示我的电子邮箱</span>
                    </div>

                    <div class="settings-option">
                      <label class="switch">
                        <input type="checkbox" v-model="privacySettings.showStatistics">
                        <span class="slider"></span>
                      </label>
                      <span>显示我的辩论统计数据</span>
                    </div>

                    <div class="settings-option">
                      <label class="switch">
                        <input type="checkbox" v-model="privacySettings.allowNotifications">
                        <span class="slider"></span>
                      </label>
                      <span>接收电子邮件通知</span>
                    </div>

                    <button
                      class="btn btn--primary mt-3"
                      @click="savePrivacySettings"
                      :disabled="loadingPrivacy"
                    >
                      {{ loadingPrivacy ? '保存中...' : '保存设置' }}
                    </button>

                    <div v-if="privacyMessage" class="form-message" :class="privacySuccess ? 'success' : 'error'">
                      {{ privacyMessage }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 编辑资料模态框 -->
      <div v-if="showEditProfile" class="modal-overlay" @click.self="showEditProfile = false">
        <div class="modal">
          <div class="modal__header">
            <h2 class="modal__title">编辑个人资料</h2>
            <button class="modal__close" @click="showEditProfile = false">×</button>
          </div>

          <div class="modal__body">
            <form @submit.prevent="updateProfile" class="form">
              <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input
                  type="text"
                  id="username"
                  class="form-control"
                  v-model="profileForm.username"
                  :class="{ 'is-invalid': profileErrors.username }"
                  required
                />
                <span v-if="profileErrors.username" class="form-text text-error">
                  {{ profileErrors.username }}
                </span>
              </div>

              <div class="form-group">
                <label class="form-label" for="bio">个人简介</label>
                <textarea
                  id="bio"
                  class="form-control"
                  v-model="profileForm.bio"
                  rows="3"
                ></textarea>
              </div>

              <div class="form-group">
                <label class="form-label">头像</label>
                <div class="avatar-upload">
                  <div class="avatar-preview">
                    <img :src="profileForm.avatar || user.avatar || 'http://placehold.co/150'" alt="头像预览">
                  </div>
                  <input
                    type="file"
                    id="avatarUpload"
                    class="avatar-input"
                    @change="handleAvatarChange"
                    accept="image/*"
                  />
                  <label for="avatarUpload" class="btn btn--outline">选择图片</label>
                </div>
              </div>
            </form>
          </div>

          <div class="modal__footer">
            <button class="btn btn--outline" @click="showEditProfile = false">取消</button>
            <button
              class="btn btn--primary"
              @click="updateProfile"
              :disabled="loadingProfile"
            >
              {{ loadingProfile ? '保存中...' : '保存' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script src="./js/UserCenter.js"></script>

<style lang="scss">
@use './scss/UserCenter.scss';
</style>



<style lang="scss" scoped>
.settings-card {
  padding: 1.5rem;
  margin-bottom: 1rem;
  background: var(--color-card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.settings-form {
  max-width: 600px;
}

.settings-group {
  margin-bottom: 2rem;

  &__title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--color-text-primary);
  }
}

.settings-option {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;

  .switch {
    margin-right: 1rem;
  }
}

.settings-description {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin-top: 0.5rem;
}

.info-item {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;

  .info-label {
    color: var(--color-text-secondary);
    min-width: 120px;
  }

  .info-value {
    color: var(--color-text-primary);
  }
}

.device-list {
  .device-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-sm);
    margin-bottom: 0.75rem;

    .device-icon {
      font-size: 1.5rem;
      margin-right: 1rem;
    }

    .device-info {
      flex: 1;

      .device-name {
        font-weight: 500;
        color: var(--color-text-primary);
      }

      .device-meta {
        font-size: 0.9rem;
        color: var(--color-text-secondary);
      }
    }
  }
}

.empty-message {
  color: var(--color-text-secondary);
  text-align: center;
  padding: 1rem;
}

.btn--danger {
  background-color: var(--color-danger);
  color: white;

  &:hover {
    background-color: var(--color-danger-dark);
  }
}

.btn--small {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
</style>
