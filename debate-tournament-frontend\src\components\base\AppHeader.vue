<template>
  <el-header class="app-header" height="auto">
    <div class="container">
      <div class="app-header__content">
        <!-- Logo -->
        <div class="app-header__logo">
          <router-link to="/" class="logo">
            <el-image class="logo__image" src="/logo.png" alt="AI辩论赛" fit="contain">
              <template #error>
                <div class="logo__text">AI辩论赛</div>
              </template>
            </el-image>
          </router-link>
        </div>

        <!-- 导航菜单 - 桌面版 -->
        <el-menu
          class="app-header__nav"
          mode="horizontal"
          :ellipsis="false"
          :router="true"
          :default-active="activeRoute"
        >
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/debate-hall">辩论大厅</el-menu-item>
          <el-menu-item index="/about">关于我们</el-menu-item>
          <el-sub-menu index="resources">
            <template #title>资源</template>
            <el-menu-item index="/help-center">帮助中心</el-menu-item>
            <el-menu-item index="/faq">常见问题</el-menu-item>
            <el-menu-item index="/contact">联系我们</el-menu-item>
          </el-sub-menu>
        </el-menu>

        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-toggle" @click="toggleMobileMenu">
          <el-icon :size="24" v-if="!mobileNavVisible"><Menu /></el-icon>
          <el-icon :size="24" v-else><Close /></el-icon>
        </div>

        <!-- 用户菜单 -->
        <div class="app-header__user">
          <template v-if="isAuthenticated">
            <el-dropdown trigger="click" @command="handleCommand">
              <div class="user-menu">
                <el-avatar :size="32" :src="userAvatar">{{ userName.charAt(0) }}</el-avatar>
                <span class="user-name hidden-sm-and-down">{{ userName }}</span>
                <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="user-center">
                    <el-icon><User /></el-icon>个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <div class="auth-buttons">
              <router-link to="/login">
                <el-button>登录</el-button>
              </router-link>
              <router-link to="/register">
                <el-button type="primary">注册</el-button>
              </router-link>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <el-collapse-transition>
      <div class="mobile-nav" v-show="mobileNavVisible">
        <div class="mobile-nav__content">
          <el-menu
            :router="true"
            :default-active="activeRoute"
            class="mobile-nav__menu"
          >
            <el-menu-item index="/" @click="closeMobileMenu">
              <el-icon><HomeFilled /></el-icon>首页
            </el-menu-item>
            <el-menu-item index="/debate-hall" @click="closeMobileMenu">
              <el-icon><ChatDotRound /></el-icon>辩论大厅
            </el-menu-item>
            <el-menu-item index="/ai-models" @click="closeMobileMenu">
              <el-icon><Monitor /></el-icon>AI模型
            </el-menu-item>
            <el-menu-item index="/about" @click="closeMobileMenu">
              <el-icon><InfoFilled /></el-icon>关于我们
            </el-menu-item>
            <el-sub-menu index="resources">
              <template #title>
                <el-icon><Document /></el-icon>资源
              </template>
              <el-menu-item index="/help-center" @click="closeMobileMenu">帮助中心</el-menu-item>
              <el-menu-item index="/faq" @click="closeMobileMenu">常见问题</el-menu-item>
              <el-menu-item index="/contact" @click="closeMobileMenu">联系我们</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>
      </div>
    </el-collapse-transition>
  </el-header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '@/store/user';
import {
  Menu, Close, User, Setting, SwitchButton,
  HomeFilled, ChatDotRound, Monitor, InfoFilled,
  Document, ArrowDown
} from '@element-plus/icons-vue';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 计算当前激活的路由
const activeRoute = computed(() => route.path);

// 计算属性
const isAuthenticated = computed(() => userStore.isLoggedIn);
const userName = computed(() => userStore.userInfo?.username || userStore.username || '用户');
const userAvatar = computed(() => userStore.userInfo?.avatar || userStore.avatar || '');

// 用户菜单状态
const userMenuVisible = ref(false);

// 移动端导航状态
const mobileNavVisible = ref(false);

/**
 * 切换移动端导航
 */
function toggleMobileMenu() {
  mobileNavVisible.value = !mobileNavVisible.value;
  // 如果打开移动端导航，则禁止滚动
  document.body.style.overflow = mobileNavVisible.value ? 'hidden' : '';
}

/**
 * 关闭移动导航
 */
function closeMobileMenu() {
  mobileNavVisible.value = false;
  document.body.style.overflow = '';
}

/**
 * 处理下拉菜单命令
 */
function handleCommand(command) {
  if (command === 'logout') {
    handleLogout();
  } else if (command === 'user-center') {
    router.push('/user');
  } else if (command === 'settings') {
    router.push('/settings');
  }
}

/**
 * 处理登出
 */
async function handleLogout() {
  try {
    await userStore.logout();
    mobileNavVisible.value = false;
    router.push('/');
  } catch (error) {
    console.error('登出失败:', error);
  }
}

// 监听滚动，自动收起移动导航
function handleScroll() {
  if (mobileNavVisible.value) {
    closeMobileMenu();
  }
}

// 监听屏幕尺寸变化，在大屏时关闭移动导航
function handleResize() {
  if (window.innerWidth >= 768 && mobileNavVisible.value) {
    closeMobileMenu();
  }
}

// 生命周期钩子
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
  window.removeEventListener('resize', handleResize);
  // 确保在组件卸载时恢复滚动
  document.body.style.overflow = '';
});
</script>

<style lang="scss">
.app-header {
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;

  .container {
    height: 100%;
  }

  &__content {
    display: flex;
    align-items: center;
    height: 64px;
  }

  &__logo {
    margin-right: 40px;

    .logo {
      display: flex;
      align-items: center;
      text-decoration: none;

      &__image {
        height: 40px;
        width: auto;
      }

      &__text {
        font-size: 20px;
        font-weight: bold;
        color: var(--primary-color);
      }
    }
  }

  &__nav {
    flex: 1;
    border-bottom: none;

    @media (max-width: 768px) {
      display: none;
    }
  }

  &__user {
    margin-left: 20px;

    .user-menu {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 5px;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      .user-name {
        margin: 0 8px;
        font-size: 14px;
        color: var(--text-regular);
      }
    }

    .auth-buttons {
      display: flex;
      gap: 10px;
    }
  }
}

.mobile-menu-toggle {
  display: none;
  cursor: pointer;
  margin-left: auto;

  @media (max-width: 768px) {
    display: flex;
    align-items: center;
  }
}

.mobile-nav {
  position: fixed;
  top: 64px;
  left: 0;
  width: 100%;
  height: calc(100vh - 64px);
  background-color: #fff;
  z-index: 999;
  overflow-y: auto;

  &__content {
    padding: 20px 0;
  }

  &__menu {
    border-right: none;
  }
}

// 响应式辅助类
@media (max-width: 768px) {
  .hidden-sm-and-down {
    display: none !important;
  }
}
</style>
