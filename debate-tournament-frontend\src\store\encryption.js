import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import CryptoJS from 'crypto-js';
import JSEncrypt from 'jsencrypt';

export const useEncryptionStore = defineStore('encryption', () => {
  // 状态
  const publicKey = ref('');
  const publicKeyId = ref('');
  const sessionKey = ref('');
  const keyExpiresAt = ref(0);
  const isInitialized = ref(false);
  const sensitiveFieldSalt = ref(''); // 敏感字段加盐

  // 计算属性
  const isKeyExpired = computed(() => {
    return Date.now() > keyExpiresAt.value;
  });

  // 初始化加密系统
  async function initialize() {
    if (isInitialized.value && !isKeyExpired.value) {
      return true;
    }

    try {
      // 从服务器获取公钥
      const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
      const response = await fetch(`${baseURL}/auth/encryption/key`);
      const data = await response.json();

      if (!data.data) {
        throw new Error('获取公钥失败');
      }

      publicKey.value = data.data.publicKey;
      publicKeyId.value = data.data.keyId;
      keyExpiresAt.value = Date.now() + data.data.expiresIn * 1000;
      sensitiveFieldSalt.value = CryptoJS.lib.WordArray.random(16).toString();

      // 生成随机会话密钥
      sessionKey.value = CryptoJS.lib.WordArray.random(32).toString();

      isInitialized.value = true;
      return true;
    } catch (error) {
      console.error('初始化加密系统失败', error);
      return false;
    }
  }

  // 使用RSA加密会话密钥
  function encryptSessionKey() {
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey(publicKey.value);
    return encrypt.encrypt(sessionKey.value);
  }

  // 使用AES加密请求数据
  function encryptData(data) {
    const jsonData = JSON.stringify(data);
    return CryptoJS.AES.encrypt(jsonData, sessionKey.value).toString();
  }

  // 使用AES解密响应数据
  function decryptData(encryptedData) {
    const bytes = CryptoJS.AES.decrypt(encryptedData, sessionKey.value);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedData);
  }

  // 生成请求签名
  function generateSignature(data) {
    const jsonData = typeof data === 'string' ? data : JSON.stringify(data);
    return CryptoJS.HmacSHA256(jsonData, sessionKey.value).toString();
  }

  // 验证响应签名
  function verifySignature(data, signature) {
    const calculatedSignature = generateSignature(data);
    return calculatedSignature === signature;
  }

  // 敏感信息二次加密（如密码等）
  function encryptSensitiveField(value) {
    // 先使用PBKDF2进行密钥派生
    const key = CryptoJS.PBKDF2(sessionKey.value, sensitiveFieldSalt.value, {
      keySize: 256 / 32,
      iterations: 1000
    });

    // 使用派生密钥进行AES加密
    return CryptoJS.AES.encrypt(value, key.toString()).toString();
  }

  // 敏感信息加密标记
  function markSensitiveFields(data, sensitiveFields = ['password', 'oldPassword', 'newPassword', 'confirmPassword', 'securityAnswer']) {
    if (!data || typeof data !== 'object') return data;

    const result = {...data};

    // 遍历所有敏感字段并加密
    sensitiveFields.forEach(field => {
      if (result[field]) {
        result[field] = encryptSensitiveField(result[field]);
        result[`${field}_encrypted`] = true; // 标记该字段已被加密
      }
    });

    return result;
  }

  // 加密请求
  function encryptRequest(data, options = {}) {
    // 默认配置
    const config = {
      encryptSensitiveFields: true,
      sensitiveFields: ['password', 'oldPassword', 'newPassword', 'confirmPassword', 'securityAnswer'],
      ...options
    };

    // 处理敏感字段
    let processedData = data;
    if (config.encryptSensitiveFields) {
      processedData = markSensitiveFields(data, config.sensitiveFields);
    }

    // 添加时间戳防止重放攻击
    const requestData = {
      ...processedData,
      timestamp: Date.now(),
      clientNonce: CryptoJS.lib.WordArray.random(16).toString() // 添加随机数防止重放
    };

    // 加密数据
    const encryptedData = encryptData(requestData);

    // 生成签名
    const signature = generateSignature(encryptedData);

    // 如果会话密钥是新的，则加密并发送
    let encryptedSessionKey = null;
    if (!isInitialized.value || isKeyExpired.value) {
      initialize();
      encryptedSessionKey = encryptSessionKey();
    }

    return {
      data: encryptedData,
      signature,
      sessionKey: encryptedSessionKey,
      keyId: publicKeyId.value
    };
  }

  // 解密响应
  function decryptResponse(response) {
    // 验证签名
    if (!verifySignature(response.data, response.signature)) {
      throw new Error('响应签名验证失败，可能存在安全风险');
    }

    // 解密数据
    const decryptedData = decryptData(response.data);

    // 验证时间戳
    const responseTime = decryptedData.timestamp || 0;
    const currentTime = Date.now();
    if (Math.abs(currentTime - responseTime) > 60000) { // 1分钟有效期
      throw new Error('响应时间戳验证失败，可能是重放攻击');
    }

    return decryptedData;
  }

  // 刷新密钥
  async function refreshKeys() {
    isInitialized.value = false;
    return await initialize();
  }

  // 创建加密HTTP请求
  async function encryptedFetch(url, options = {}) {
    await initialize();

    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      encryptSensitiveFields: true
    };

    const mergedOptions = {...defaultOptions, ...options};

    // 加密请求体
    if (mergedOptions.body) {
      const requestData = typeof mergedOptions.body === 'string' ? JSON.parse(mergedOptions.body) : mergedOptions.body;
      const encryptedRequest = encryptRequest(requestData, {
        encryptSensitiveFields: mergedOptions.encryptSensitiveFields,
        sensitiveFields: mergedOptions.sensitiveFields
      });
      mergedOptions.body = JSON.stringify(encryptedRequest);
    }

    // 确保URL包含完整的基础URL
    const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';
    const fullUrl = url.startsWith('http') ? url : `${baseURL}${url.startsWith('/') ? url : '/' + url}`;

    // 发送请求
    const response = await fetch(fullUrl, mergedOptions);
    const responseData = await response.json();

    // 检查是否是加密响应
    if (responseData.data && responseData.signature) {
      return decryptResponse(responseData);
    }

    return responseData;
  }

  return {
    publicKey,
    publicKeyId,
    sessionKey,
    keyExpiresAt,
    isInitialized,
    isKeyExpired,
    initialize,
    encryptSessionKey,
    encryptData,
    decryptData,
    generateSignature,
    verifySignature,
    encryptRequest,
    decryptResponse,
    refreshKeys,
    encryptSensitiveField,
    encryptedFetch
  };
});
